<?php
// ====== CONFIGURATION ======

// Map bot IDs (1–44) to your tokens
$botTokens = [
    '1'  => '7324085708:AAECLspc6xqyZZ7lekSsm1ZS6PDhsWRejAE',
    '2'  => '7734048562:AAFPk0NBRHwGyFb-hDtt_Br9i6HlZ5IKd1g',
    '3'  => '8000823167:AAG139icfPTOrXUDbR1lEtYOBsJ2IqzcU7E',
    '4'  => '8121324542:AAFsEWUTlbX4_BcaTE8XAoA2WYO2sxHhwKQ',
    '5'  => '7043095943:AAGhPspmxSlvT1yzT9ARIcT3FFr9pO5FlxI',
    '6'  => '8070640534:AAF0SzNYuqxOQ8DvoIzpgA9LnNjAGWE9gho',
    '7'  => '7414728357:AAFWdAq0faNm2Yczhm0qNAX0-Tqo1u6jrAQ',
    '8'  => '7595655197:AAE-LVH3ao4rRRCeOMHdnqw_hgv7tMxPbiI',
    '9'  => '7631470660:AAGF6R8_udL0tWVqZWTFF43Z8wdYQoRDYSU',
    '10' => '8181651796:AAE2aVmc52tPVBofhzj5RbVvPNZKWQiYL3o',
    '11' => '7807349163:AAGr1S0OS4O09YP-PKxVTXQwh5fgss3VdS4',
    '12' => '7224250149:AAFg8tIbi8xsJHVLZSVv52NHtAfxyJ7p7IA',
    '13' => '7339986151:AAETfpuU3ksUtk5GSkLkPsrb9tGiccSb9dg',
    '14' => '7748840812:AAHDjQVZL1NAkHUMUYGuBqu0sAudlXEc2Uo',
    '15' => '7728027518:AAEBUOi_BPYqqu8qXOvYQfM9GHfVUDwJ0-M',
    '16' => '7601378202:AAHi2Jo0t_W8sEWnxWkDlZqjYAwpUGimG6k',
    '17' => '8005056905:AAGBc7HjP4gvAlrnKrelz94A4mNSgDBqLmg',
    '18' => '5920970906:AAEl59pHfUw0WnqkPnR5VH4HWq35mL_m0u0',
    '19' => '7957888180:AAEsxFpBqlwI1snuXdLxBM9feYo8GrxL71U',
    '20' => '1874727878:AAGhz33r6JB7jf5LA5GmXKuu-NkKE-juO4Y',
    '21' => '1782934509:AAHAvy6rIDj9WOi3N6Waz_MbSbzghrDBsb0',
    '22' => '1854747800:AAFrh45kpaQ7UOMx7rF4YOADrFCh2P3x5Fs',
    '23' => '1812296269:AAE8KpwmmO73Bg9uIxgiihvhz8wd6P-0-ww',
    '24' => '1833448524:AAE1C4jzssdPGA5iYlmrNtWchE6I0E6gURw',
    '25' => '2054730466:AAGaTrFU4UvA2qrt2vmUTBB72G1UFzg7MXk',
    '26' => '2103424776:AAGkaXs8NqiFjfoXJ7G2RPzNMnhpLXCJG98',
    '27' => '5232551378:AAEA6kDOIAUPhl44Eqzo_jTJNV09cHa3Ifg',
    '28' => '5076605250:AAFIwFnhMd16iW27l5SOszE1KVU_X3dLbA4',
    '29' => '5116250996:AAG-a_xoeczvkCr_Z_Ggus9Fu_C49OqgmZ4',
    '30' => '7710186495:AAF46DoNSUQ9PEEDwREx8aOK42Du1rhyXoE',
    '31' => '5989552141:AAGEQ1ObcCmaO68EvwU3XFMRuRHODSAv2o0',
    '32' => '5954755638:AAHyHoMUMmkTgFON6Cwzkq4Pu0bXMVrZKo4',
    '33' => '7638711403:AAFlCDzFZohgrW-duuPiijRMRT-BtKO9a2s',
    '34' => '7992531992:AAHgqBoygZ-vEvGiihwGuaMwG5d3v3BQ-TI',
    '35' => '7680297842:AAGPmB2yufn-n6-xNLIBag5eYEchh6pIF48',
    '36' => '7529351450:AAGFFKB4VadwrZTZjjyqBhuS-bCDMi2fB0s',
    '37' => '7611442057:AAGnhvfJg_EnOITI5mrEhN8p5ehGgiM1ax0',
    '38' => '7496411088:AAF5_Hz4nysgtimtZ0xo1-QcLqFn4dAyEh0',
    '39' => '8171836495:AAFUHd4K7OcuCfnXVqpEoQFBLd4bhxf4maA',
    '40' => '7309785917:AAEDGLAsBw5yEraoPhSO_PfYCkvSyNwGHts',
    '41' => '7464545372:AAGiey-QGPxpfVnBMB3eaMe-MkBV5JVAJus',
    '42' => '7504410338:AAF7rlo9UZAAwiGl6WMwQ8DqQAfQGCTii-o',
    '43' => '7611709153:AAGEjbgr-KZcyDMn7Z5dfQwZZPgLSHssp6Y',
    '44' => '8000444345:AAHicXHhUnRptpI3yp26MB0Af_9rWC9t3QE',
];

// ====== SEQUENTIAL REACTION CONFIGURATION ======

// Target channels for sequential reactions (add/remove channel IDs as needed)
$sequentialChannelIds = [
    -1002888471861, -1002703213555 // ZEFINCAPITALFX - Original sequential channel
    // Add more channel IDs here to enable sequential reactions
    // Example: -1001234567890,  // Another channel name
];

// Legacy variable for backward compatibility (will be removed in future)
$sequentialChannelId = !empty($sequentialChannelIds) ? $sequentialChannelIds[0] : null;

// Sequential reaction emoji pool - randomly selected for each reaction
$sequentialEmojiPool = ['❤️', '🤩', '💯', '🥰', '🍾', '🎉', '💯', '❤‍🔥', '🏆', '😘', '🫡'];

// Reaction delay interval in seconds (20 seconds between each bot)
$reactionDelayInterval = 20;

// Maximum number of bots to use for sequential reactions
$maxSequentialBots = 44;

// ====== LEGACY CONFIGURATION (for non-sequential channels) ======

// Fixed reaction blocks for legacy behavior
$emojiBlock1 = '❤️';  // bots 1–20
$emojiBlock2 = '👍';  // bots 21–30
$randomPool  = ['🔥','🥰','👏','🎉']; // bots 31–44

// Special channel configuration for legacy behavior
$specialChannelId = -1002140916089;
$specialEmojiPool = ['❤️‍🔥', '🐳', '🦄', '💯', '🔥']; // Sequential assignment for special channel using modulo

// Custom mixed reaction channel configuration
$customMixedChannelId = -1001907948187;
$customMixedHeartEmoji = '❤️';
$customMixedOtherPool = ['👍', '🔥', '🥰', '👏', '🎉', '🤩', '💯', '⚡️', '🏆', '😘', '😎']; // Pool for non-heart reactions

// Group reaction configuration
$groupReactionPool = ['👍', '🔥', '🥰', '👏', '🎉', '🤩', '💯', '⚡️', '🏆', '😘', '😎']; // Random reactions for group posts

// ====== FILE PATHS ======

// Log file path
define('LOGFILE', __DIR__ . '/bot_reactions.log');

// Reaction queue file path
define('QUEUE_FILE', __DIR__ . '/reaction_queue.json');

// ====== HELPER FUNCTIONS ======

// — Logging helper —
function logLine($line) {
    file_put_contents(LOGFILE, date('[Y-m-d H:i:s] ') . $line . "\n", FILE_APPEND);
}

// — Queue management functions —
function loadQueue() {
    if (!file_exists(QUEUE_FILE)) {
        return [];
    }
    $content = file_get_contents(QUEUE_FILE);
    return $content ? json_decode($content, true) : [];
}

function saveQueue($queue) {
    file_put_contents(QUEUE_FILE, json_encode($queue, JSON_PRETTY_PRINT));
}

function addToQueue($chatId, $messageId, $postTime) {
    global $maxSequentialBots, $reactionDelayInterval, $sequentialEmojiPool;

    $queue = loadQueue();

    // Create reactions for bots 2-N (Bot 1 already reacted immediately)
    for ($botNum = 2; $botNum <= $maxSequentialBots; $botNum++) {
        $scheduledTime = $postTime + (($botNum - 1) * $reactionDelayInterval);
        $randomEmoji = $sequentialEmojiPool[array_rand($sequentialEmojiPool)];

        $queue[] = [
            'chat_id' => $chatId,
            'message_id' => $messageId,
            'bot_id' => $botNum,
            'emoji' => $randomEmoji,
            'scheduled_time' => $scheduledTime,
            'post_time' => $postTime,
            'created_at' => time()
        ];
    }

    // Sort queue by scheduled time to maintain FIFO order
    usort($queue, function($a, $b) {
        return $a['scheduled_time'] - $b['scheduled_time'];
    });

    saveQueue($queue);
    $queuedBots = $maxSequentialBots - 1; // Exclude Bot 1 which reacted immediately
    logLine("Added {$queuedBots} reactions to queue for message {$messageId} in chat {$chatId}");
}

function processQueue() {
    global $botTokens;

    $queue = loadQueue();
    $currentTime = time();
    $processedCount = 0;
    $remainingQueue = [];

    foreach ($queue as $reaction) {
        if ($reaction['scheduled_time'] <= $currentTime) {
            // Time to execute this reaction
            $botId = $reaction['bot_id'];
            $botToken = $botTokens[(string)$botId] ?? null;

            if ($botToken) {
                $apiURL = "https://api.telegram.org/bot{$botToken}/";
                $emoji = $reaction['emoji'];
                $chatId = $reaction['chat_id'];
                $messageId = $reaction['message_id'];

                // Execute the reaction
                $response = file_get_contents(
                    $apiURL
                    . "setMessageReaction?chat_id={$chatId}"
                    . "&message_id={$messageId}"
                    . "&reaction=[{\"type\":\"emoji\",\"emoji\":\"{$emoji}\"}]"
                    . "&is_big=true"
                );

                $delay = $currentTime - $reaction['post_time'];
                logLine("SEQUENTIAL: Bot #{$botId} → '{$emoji}' (Message: {$messageId}, Delay: {$delay}s)");
                $processedCount++;
            } else {
                logLine("ERROR: Bot token not found for bot #{$botId}");
            }
        } else {
            // Keep this reaction in the queue for later
            $remainingQueue[] = $reaction;
        }
    }

    // Save the updated queue
    saveQueue($remainingQueue);

    if ($processedCount > 0) {
        logLine("Processed {$processedCount} reactions from queue. " . count($remainingQueue) . " reactions remaining.");
    }

    return $processedCount;
}

// ====== MAIN EXECUTION ======

// Always process the queue first on every webhook call
processQueue();

// Read incoming update
$rawInput = file_get_contents('php://input');
$update = json_decode($rawInput, true);

// Log ALL webhook attempts (even if bot is not a member)
logLine("WEBHOOK_RAW: " . $rawInput);
logLine("WEBHOOK_BOT_ID: " . ($_GET['bot_id'] ?? 'MISSING'));
logLine("WEBHOOK_TIMESTAMP: " . date('Y-m-d H:i:s'));

// Debug logging for troubleshooting
$debugInfo = [
    'bot_id' => $_GET['bot_id'] ?? 'MISSING',
    'timestamp' => date('Y-m-d H:i:s'),
    'has_channel_post' => isset($update['channel_post']),
    'has_group_message' => isset($update['message']) && isset($update['message']['chat']['type']) && $update['message']['chat']['type'] === 'supergroup',
    'update_keys' => $update ? array_keys($update) : 'NULL_UPDATE'
];

if (isset($update['channel_post'])) {
    $debugInfo['chat_id'] = $update['channel_post']['chat']['id'] ?? 'MISSING';
    $debugInfo['message_id'] = $update['channel_post']['message_id'] ?? 'MISSING';
} elseif (isset($update['message'])) {
    $debugInfo['chat_id'] = $update['message']['chat']['id'] ?? 'MISSING';
    $debugInfo['message_id'] = $update['message']['message_id'] ?? 'MISSING';
    $debugInfo['chat_type'] = $update['message']['chat']['type'] ?? 'MISSING';
}

logLine("WEBHOOK_DEBUG: " . json_encode($debugInfo));

// Validate bot_id
if (!isset($_GET['bot_id'], $botTokens[$_GET['bot_id']])) {
    http_response_code(400);
    exit("Invalid or missing bot_id");
}
$botId    = intval($_GET['bot_id']);
$botToken = $botTokens[(string)$botId];
$apiURL   = "https://api.telegram.org/bot{$botToken}/";

// React to both channel_post and message (for groups)
$isChannelPost = isset($update['channel_post']);
$isGroupMessage = isset($update['message']) && in_array($update['message']['chat']['type'], ['group', 'supergroup']);

if (!$isChannelPost && !$isGroupMessage) {
    logLine("WEBHOOK_IGNORED: Not a channel post or group message");
    exit;
}

// Extract chat_id and message_id based on post type
if ($isChannelPost) {
    $chat_id    = $update['channel_post']['chat']['id'];
    $message_id = $update['channel_post']['message_id'];
} else {
    $chat_id    = $update['message']['chat']['id'];
    $message_id = $update['message']['message_id'];
}

// Check if this is a group message (handle differently from channels)
if ($isGroupMessage) {
    // Implement multi-bot reaction system for groups
    // The receiving bot will trigger reactions from multiple other bots

    logLine("GROUP_WEBHOOK: Bot #{$botId} received webhook for group {$chat_id}, message {$message_id}");

    // Define which bots should react to group messages (you can customize this list)
    $groupReactionBots = [1, 2, 3, 4, 5]; // Add more bot IDs as needed

    // Send reactions from multiple bots
    foreach ($groupReactionBots as $reactionBotId) {
        if (!isset($botTokens[(string)$reactionBotId])) {
            logLine("ERROR: Bot token not found for bot #{$reactionBotId}");
            continue;
        }

        $reactionBotToken = $botTokens[(string)$reactionBotId];
        $reactionApiURL = "https://api.telegram.org/bot{$reactionBotToken}/";

        // Use random reactions from the group reaction pool
        global $groupReactionPool;
        $reaction = $groupReactionPool[array_rand($groupReactionPool)];

        logLine("GROUP: Bot #{$reactionBotId} → '{$reaction}' (Group: {$chat_id}, Message: {$message_id})");

        // Send reaction using cURL with JSON
        $reactionUrl = $reactionApiURL . "setMessageReaction";
        $postData = json_encode([
            "chat_id"    => $chat_id,
            "message_id" => $message_id,
            "reaction"   => [
                [
                    "type"  => "emoji",
                    "emoji" => $reaction
                ]
            ],
            "is_big"     => true
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $reactionUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type: application/json"]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            logLine("ERROR: cURL error for bot #{$reactionBotId} in group {$chat_id}: " . curl_error($ch));
        } else {
            $responseData = json_decode($response, true);
            if (!$responseData || !$responseData['ok']) {
                $errorMsg = $responseData['description'] ?? 'Unknown error';
                logLine("ERROR: Telegram API error for bot #{$reactionBotId} in group {$chat_id}: " . $errorMsg);
            } else {
                logLine("SUCCESS: Bot #{$reactionBotId} reaction sent to group {$chat_id}, message {$message_id}");
            }
        }
        curl_close($ch);

        // Small delay between reactions to avoid rate limiting
        usleep(100000); // 0.1 second delay
    }

    exit; // Exit after handling group message
}

// Check if this is a sequential reaction channel (only for channel posts)
$isSequentialChannel = !empty($sequentialChannelIds) && in_array($chat_id, $sequentialChannelIds);
logLine("SEQUENTIAL_CHECK: botId={$botId}, sequentialChannels=[" . implode(',', $sequentialChannelIds) . "], chat_id={$chat_id}, isSequential=" . ($isSequentialChannel ? 'YES' : 'NO'));

if ($isSequentialChannel) {
    logLine("SEQUENTIAL_MATCH: Channel {$chat_id} matched sequential channels, botId={$botId}");

    // Sequential reaction mode - only Bot 1 handles the initial setup
    if ($botId == 1) {
        logLine("SEQUENTIAL_BOT1: Starting Bot #1 sequential processing");

        $postTime = time();
        addToQueue($chat_id, $message_id, $postTime);

        // Bot 1 reacts immediately with a random emoji
        $reaction = $sequentialEmojiPool[array_rand($sequentialEmojiPool)];

        // Log it
        logLine("SEQUENTIAL: Bot #1 → '{$reaction}' (IMMEDIATE, Channel: {$chat_id})");

        // Send immediate reaction
        file_get_contents(
            $apiURL
            . "setMessageReaction?chat_id={$chat_id}"
            . "&message_id={$message_id}"
            . "&reaction=[{\"type\":\"emoji\",\"emoji\":\"{$reaction}\"}]"
            . "&is_big=true"
        );

        logLine("SEQUENTIAL_BOT1: Completed Bot #1 processing");
    } else {
        logLine("SEQUENTIAL_OTHER: Bot #{$botId} exiting (not Bot #1)");
    }
    // Other bots do nothing for new posts - they'll react via the queue system
    exit;
} else {
    logLine("SEQUENTIAL_NO_MATCH: Channel {$chat_id} not in sequential channels, using legacy behavior for botId={$botId}");
}

// ====== LEGACY BEHAVIOR FOR NON-SEQUENTIAL CHANNELS ======

// Determine emoji based on channel (legacy behavior)
if ($chat_id == $specialChannelId) {
    // Special channel: sequential emoji assignment using modulo logic
    // Bot 1→❤️‍🔥, Bot 2→🐳, Bot 3→🦄, Bot 4→💯, Bot 5→🔥, Bot 6→❤️‍🔥, etc.
    $reaction = $specialEmojiPool[($botId - 1) % 5];
} elseif ($chat_id == $customMixedChannelId) {
    // Custom mixed reaction channel: 15-20 bots use heart, others use random reactions

    // Generate a consistent seed based on message_id to ensure same bots always use hearts for the same message
    $seed = $message_id % 1000; // Use message ID as seed for consistency
    mt_srand($seed);

    // Determine how many bots will use heart emoji (15-20 randomly)
    $heartBotCount = mt_rand(15, 20);

    // Create array of bot IDs that will use heart emoji
    $allBotIds = range(1, 44);
    shuffle($allBotIds); // Shuffle with the seeded random
    $heartBotIds = array_slice($allBotIds, 0, $heartBotCount);

    // Reset random seed to current time for other random operations
    mt_srand();

    if (in_array($botId, $heartBotIds)) {
        $reaction = $customMixedHeartEmoji;
        logLine("CUSTOM_MIXED: Bot #{$botId} assigned HEART emoji (one of {$heartBotCount} heart bots)");
    } else {
        $reaction = $customMixedOtherPool[array_rand($customMixedOtherPool)];
        logLine("CUSTOM_MIXED: Bot #{$botId} assigned OTHER emoji '{$reaction}' (not in heart group)");
    }
} else {
    // Default behavior for all other channels
    if ($botId >= 1 && $botId <= 20) {
        $reaction = $emojiBlock1;
    } elseif ($botId >= 21 && $botId <= 30) {
        $reaction = $emojiBlock2;
    } else {
        $reaction = $randomPool[array_rand($randomPool)];
    }
}

// Log it (skip logging for custom mixed channel as it's already logged above)
if ($chat_id != $customMixedChannelId) {
    logLine("LEGACY: Bot #{$botId} → '{$reaction}' (Channel: {$chat_id})");
}

// Send reaction
file_get_contents(
    $apiURL
    . "setMessageReaction?chat_id={$chat_id}"
    . "&message_id={$message_id}"
    . "&reaction=[{\"type\":\"emoji\",\"emoji\":\"{$reaction}\"}]"
    . "&is_big=true"
);
