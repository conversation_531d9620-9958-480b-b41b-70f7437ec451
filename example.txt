<?php 
// ====== CONFIGURATION ======
$botToken         = "7891979058:AAHXsIC65dlGH-Rq-llvtOds2KFmHyx-dng";
$adminID          = 1049516929;

// Primary update channel (existing)
$updateChannel       = "-1002414699235";
$updateLink          = "https://t.me/+4y918wz7i0Y3MDM9";

// New forced subscription channel
$forceSubChannel     = "-1001675894903";
$forceSubLink        = "https://t.me/+4jMsNvA4p4YxODRl";

$apiURL           = "https://api.telegram.org/bot$botToken/";
// Set your bot's username (without the @)
$botUsername      = "auto_reaction0_bot";

// List of reaction emojis (for auto reactions)
$reactions        = ['👍', '❤️', '🔥', '🥰', '👏', '😁', '🎉', '🤩', '🙏', '👌', '🕊', '😍', '🐳', '💯', '⚡️', '🏆'];

// ===== UTILITY FUNCTIONS =====

/**
 * sendMessage: Sends a text or photo message.
 */
function sendMessage($chat_id, $text, $parse_mode = "Markdown", $reply_markup = null, $photo = null, $reply_to_message_id = null) {
    global $apiURL;
    if ($photo) {
        $photoPath = realpath($photo);
        if (!$photoPath) {
            error_log("Photo file not found: " . $photo);
            $photo = null;
        }
    }
    if ($photo) {
        $url = $apiURL . "sendPhoto";
        $post_fields = [
            "chat_id"      => $chat_id,
            "photo"        => new CURLFile($photoPath),
            "caption"      => $text,
            "parse_mode"   => $parse_mode,
            "reply_markup" => $reply_markup
        ];
        if ($reply_to_message_id) {
            $post_fields["reply_to_message_id"] = $reply_to_message_id;
        }
    } else {
        $url = $apiURL . "sendMessage";
        $post_fields = [
            "chat_id"      => $chat_id,
            "text"         => $text,
            "parse_mode"   => $parse_mode,
            "reply_markup" => $reply_markup
        ];
        if ($reply_to_message_id) {
            $post_fields["reply_to_message_id"] = $reply_to_message_id;
        }
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type:multipart/form-data"]);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
    $result = curl_exec($ch);
    if (curl_errno($ch)) {
        error_log('Curl error: ' . curl_error($ch));
    }
    curl_close($ch);
    return $result;
}

/**
 * answerCallbackQuery: Answers inline keyboard callback queries.
 */
function answerCallbackQuery($callback_query_id, $text = "") {
    global $apiURL;
    $url = $apiURL . "answerCallbackQuery";
    $post_fields = [
        "callback_query_id" => $callback_query_id,
        "text"              => $text,
        "show_alert"        => false
    ];
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type:multipart/form-data"]);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
    curl_exec($ch);
    curl_close($ch);
}

/**
 * buildInlineKeyboard: Creates an inline keyboard layout.
 */
function buildInlineKeyboard($keyboard) {
    return json_encode(["inline_keyboard" => $keyboard]);
}

/**
 * isUserJoined: Checks if a user is a member of a channel.
 */
function isUserJoined($user_id, $channel_id) {
    global $apiURL;
    $url = $apiURL . "getChatMember?chat_id=" . $channel_id . "&user_id=" . $user_id;
    $result = @file_get_contents($url);
    if (!$result) {
        return false;
    }
    $result = json_decode($result, true);
    if (isset($result['result'])) {
        $status = $result['result']['status'];
        return !in_array($status, ["left", "kicked"]);
    }
    return false;
}

/**
 * storeChatData: Saves channel/group info to channel.json.
 */
function storeChatData($chat) {
    $file = "channel.json";
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        if (!is_array($data)) {
            $data = [];
        }
    } else {
        $data = [];
    }
    $chat_id = $chat["id"];
    if (!isset($data[$chat_id])) {
        $data[$chat_id] = [
            "id"         => $chat_id,
            "title"      => isset($chat["title"]) ? $chat["title"] : "",
            "type"       => $chat["type"],
            "username"   => isset($chat["username"]) ? $chat["username"] : "",
            "date_added" => time()
        ];
        file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    }
}

/**
 * sanitizeUserName: Cleans and sanitizes user names for safe storage.
 */
function sanitizeUserName($name) {
    if (!is_string($name) || empty($name)) {
        return "";
    }
    try {
        $name = str_replace(["\0", "\x0B"], '', $name);
        if (!mb_check_encoding($name, 'UTF-8')) {
            $name = mb_convert_encoding($name, 'UTF-8', 'auto');
        }
        $name = preg_replace('/[\x{0300}-\x{036F}]/u', '', $name);
        $name = preg_replace('/[\x{200B}-\x{200F}]/u', '', $name);
        $name = preg_replace('/[\x{202A}-\x{202E}]/u', '', $name);
        $name = preg_replace('/[\x{E000}-\x{F8FF}]/u', '', $name);
        $name = preg_replace('/[\x{F0000}-\x{FFFFD}]/u', '', $name);
        $name = preg_replace('/[\x{100000}-\x{10FFFD}]/u', '', $name);
        $name = preg_replace('/[\x{D800}-\x{DFFF}]/u', '', $name);
        $name = preg_replace('/\s+/', ' ', $name);
        $name = trim($name);
        if (mb_strlen($name, 'UTF-8') > 50) {
            $name = mb_substr($name, 0, 50, 'UTF-8');
            $name = trim($name);
        }
        if (empty($name)) {
            return "User";
        }
        return $name;
    } catch (Exception $e) {
        error_log("Error sanitizing user name: " . $e->getMessage());
        return "User";
    }
}

/**
 * storeUserData: Saves user info to usersdata.json for private chat interactions.
 */
function storeUserData($user) {
    $file = "usersdata.json";
    try {
        if (!is_array($user) || !isset($user["id"]) || !is_numeric($user["id"])) {
            error_log("Invalid user data provided to storeUserData");
            return false;
        }
        if (file_exists($file)) {
            $fileContent = file_get_contents($file);
            if ($fileContent === false) {
                error_log("Could not read usersdata.json file");
                return false;
            }
            $data = json_decode($fileContent, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log("JSON decode error in usersdata.json: " . json_last_error_msg());
                $data = [];
            }
            if (!is_array($data)) {
                $data = [];
            }
        } else {
            $data = [];
        }
        $user_id = $user["id"];
        if (!isset($data[$user_id])) {
            $firstName = isset($user["first_name"]) ? $user["first_name"] : "";
            $sanitizedFirstName = sanitizeUserName($firstName);
            $data[$user_id] = [
                "id"         => $user_id,
                "first_name" => $sanitizedFirstName,
                "date_added" => time()
            ];
            $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            if ($jsonData === false) {
                error_log("JSON encode error when saving user data: " . json_last_error_msg());
                return false;
            }
            $result = file_put_contents($file, $jsonData, LOCK_EX);
            if ($result === false) {
                error_log("Could not write to usersdata.json file");
                return false;
            }
        }
        return true;
    } catch (Exception $e) {
        error_log("Exception in storeUserData: " . $e->getMessage());
        return false;
    }
}

/**
 * setReaction: Adds a reaction (emoji) to a message.
 */
function setReaction($chat_id, $message_id, $emoji) {
    global $apiURL;
    $url = $apiURL . "setMessageReaction";
    $post_fields = json_encode([
         "chat_id"    => $chat_id,
         "message_id" => $message_id,
         "reaction"   => [
             [
                 "type"  => "emoji",
                 "emoji" => $emoji
             ]
         ],
         "is_big"     => true
    ]);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type: application/json"]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
    $result = curl_exec($ch);
    if (curl_errno($ch)) {
        error_log('Curl error in setReaction: ' . curl_error($ch));
    }
    curl_close($ch);
    return $result;
}

/**
 * checkBotAdminStatus: Checks if the bot is an admin in the specified chat.
 */
function checkBotAdminStatus($chat_id) {
    global $apiURL, $botToken;
    $botUserId = getBotUserId($botToken);
    $url = $apiURL . "getChatMember?chat_id=" . $chat_id . "&user_id=" . $botUserId;
    $result = @file_get_contents($url);
    if ($result) {
        $result = json_decode($result, true);
        if (isset($result['result']['status'])) {
            $status = $result['result']['status'];
            return in_array($status, ['administrator', 'creator']);
        }
    }
    return false;
}

/**
 * getBotUserId: Retrieves the bot's user ID.
 */
function getBotUserId($botToken) {
    global $apiURL;
    $url = $apiURL . "getMe";
    $result = @file_get_contents($url);
    if ($result) {
        $result = json_decode($result, true);
        if (isset($result['result']['id'])) {
            return $result['result']['id'];
        }
    }
    return null;
}

// ===== PROCESS INCOMING UPDATES =====
$update = json_decode(file_get_contents("php://input"), true);
if (!$update) {
    exit;
}

// -------------------------------
// 1. Handle Chat Join Requests
// -------------------------------
if (isset($update["chat_join_request"])) {
    $joinRequest = $update["chat_join_request"];
    $chat = $joinRequest["chat"];
    $from = $joinRequest["from"];
    $userChatID = $from["id"];
    storeUserData($from);
    $welcomeCaption = "👋 Hello there! I'm Free Auto Reaction Bot 🤖\n\n" .
                      "✨ I add fun emoji reactions directly to your posts and messages! 😄\n\n" .
                      "👉 Simply add me to your channel or group to get started!";
    $joinRequestKeyboard = buildInlineKeyboard([
        [
            ["text" => "🚀 Use This Bot", "url" => "https://t.me/" . $botUsername . "?start"]
        ],
        [
            ["text" => "💡 More Cool Bots", "url" => $updateLink]
        ],
        [
            ["text" => "100+ Multi Reactions😍🔥💖", "url" => "https://t.me/titanium_bots_channel/60"]
        ]
    ]);
    sendMessage($userChatID, $welcomeCaption, "Markdown", $joinRequestKeyboard, "image.jpg");
    exit;
}

// -------------------------------
// 2. Handle Membership Changes
// -------------------------------
if (isset($update["my_chat_member"])) {
    $myChatMember = $update["my_chat_member"];
    $chat         = $myChatMember["chat"];
    $old_status   = $myChatMember["old_chat_member"]["status"];
    $new_status   = $myChatMember["new_chat_member"]["status"];
    $from         = $myChatMember["from"];
    if (isset($chat["type"]) && $chat["type"] == "channel") {
        $channelName = isset($chat["title"]) ? $chat["title"] : "this channel";
        storeChatData($chat);
        storeUserData($from);
        $welcomeMsg = "🎉 Congratulations! 🎉\n\n" .
                      "Your channel <b>" . htmlspecialchars($channelName) . "</b> has been added and is now monitored. 🚀\n\n" .
                      "Thank you for choosing us! 🙏";
        sendMessage($from["id"], $welcomeMsg, "HTML");
    } elseif (isset($chat["type"]) && in_array($chat["type"], ["group", "supergroup"])) {
        if ($new_status === "member" && $old_status !== "member") {
            sendMessage($chat["id"], "please promote me as admin in this group then only i will be able to work properly and add reactions.", "Markdown");
        } elseif (in_array($new_status, ["administrator", "creator"]) && !in_array($old_status, ["administrator", "creator"])) {
            $groupMsg = "👋 Hello everyone! I'm AUTO REACTIONS BOT 🤖\n\n" .
                        "✨ I add fun emoji reactions automatically behind the scenes! 🎉";
            sendMessage($chat["id"], $groupMsg);
        }
    }
    exit;
}

// -------------------------------
// 2.5. Handle Channel Posts
// -------------------------------
if (isset($update["channel_post"])) {
    $post       = $update["channel_post"];
    $chat_id    = $post["chat"]["id"];
    $message_id = $post["message_id"];
    $randomEmoji = $reactions[array_rand($reactions)];
    setReaction($chat_id, $message_id, $randomEmoji);
    exit;
}

// -------------------------------
// 3. Handle Standard Messages & Commands
// -------------------------------
if (isset($update["message"])) {
    $message    = $update["message"];
    $chat_id    = $message["chat"]["id"];
    $user_id    = isset($message["from"]["id"]) ? $message["from"]["id"] : 0;
    $chatType   = $message["chat"]["type"];
    $text       = isset($message["text"]) ? $message["text"] : "";
    $message_id = isset($message["message_id"]) ? $message["message_id"] : 0;

    // Handle /start command.
    if (strpos($text, "/start") === 0) {
        if ($chatType == 'private' && isset($message["from"])) {
            storeUserData($message["from"]);
        }
        // Payload handling omitted for brevity...
        if ($chatType == 'private') {
            $joinedPrimary = isUserJoined($user_id, $updateChannel);
            $joinedForced  = isUserJoined($user_id, $forceSubChannel);

            if (!($joinedPrimary && $joinedForced)) {
                $joinKeyboard = buildInlineKeyboard([
                    [
                        ["text" => "🚀 Join Update Channel", "url" => $updateLink],
                        ["text" => "🚀 Join Forced Channel", "url" => $forceSubLink],
                    ],
                    [
                        ["text" => "🔄 Check Joined", "callback_data" => "check_join"]
                    ]
                ]);
                $msg = "🤖 *Welcome to Free AUTO REACTION Bot!* \n\n" .
                       "✨ To get started, please join both of our channels below:\n\n" .
                       "1️⃣ Update Channel\n2️⃣ Forced Subscription Channel\n\n" .
                       "Once you’ve joined, click _Check Joined_.";
                sendMessage($chat_id, $msg, "Markdown", $joinKeyboard, "image.jpg");
                exit;
            }

            $mainKeyboard = buildInlineKeyboard([
                [
                    ["text" => "➕ Add Your Channel", "url" => "https://t.me/{$botUsername}?startchannel=&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info"]
                ],
                [
                    ["text" => "➕ Add Your Group", "url" => "https://t.me/{$botUsername}?startgroup&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info"]
                ],
                [
                    ["text" => "💡 More Cool Bots", "url" => $updateLink]
                ],
                [
                    ["text" => "100+ Multi Reactions😍🔥💖", "url" => "https://t.me/titanium_bots_channel/60"]
                ]
            ]);
            $msg = "🎉 *Thank you for joining both channels!* \n\n" .
                   "Now you can use Auto Reaction Bot! \n\n" .
                   "Choose an option below:";
            sendMessage($chat_id, $msg, "Markdown", $mainKeyboard, "image.jpg");
            exit;
        }

        if ($chatType == 'group' || $chatType == 'supergroup') {
            $groupMsg = "helllooo guysss I am auto reaction bot.😍 I can give unlimited auto reaction in channels and groups.";
            $groupKeyboard = buildInlineKeyboard([
                [
                    ["text" => "USE ME", "url" => "https://t.me/" . $botUsername . "?start"]
                ]
            ]);
            sendMessage($chat_id, $groupMsg, "Markdown", $groupKeyboard, "image.jpg");
            if (!checkBotAdminStatus($chat_id)) {
                sendMessage($chat_id, "please promote me as admin in this group then only i will be able to work properly and add reactions.", "Markdown");
            }
            exit;
        }

        // Default private flow handled above
    }

    // Greeting auto-response in groups
    if (($chatType == 'group' || $chatType == 'supergroup') && preg_match('/\b(hi|hii|hello|hy)\b/i', $text)) {
        $groupMsg = "helllooo guysss I am auto reaction bot.😍 I can give unlimited auto reaction in channels and groups.";
        $groupKeyboard = buildInlineKeyboard([
            [
                ["text" => "USE ME", "url" => "https://t.me/" . $botUsername . "?start"]
            ]
        ]);
        sendMessage($chat_id, $groupMsg, "Markdown", $groupKeyboard, "image.jpg");
    }

    // Auto-Reaction Handler
    if (!empty($message['chat']['type'])) {
        $randomEmoji = $reactions[array_rand($reactions)];
        setReaction($chat_id, $message_id, $randomEmoji);
        if ($message['chat']['type'] == 'private') {
            if (isset($message["from"])) {
                storeUserData($message["from"]);
            }
            $privateKeyboard = buildInlineKeyboard([
                [
                    ["text" => "➕ Add to Channel", "url" => "https://t.me/{$botUsername}?startchannel=&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info"],
                    ["text" => "➕ Add to Group",   "url" => "https://t.me/{$botUsername}?startgroup&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info"]
                ],
                [
                    ["text" => "🔄 Use The Bot", "callback_data" => "use_bot"]
                ],
                [
                    ["text" => "100+ Multi Reactions😍🔥💖", "url" => "https://t.me/titanium_bots_channel/60"]
                ]
            ]);
            sendMessage($chat_id, "I am FREE AUTO REACTION BOT! JUST ADD ME TO CHANNEL AND SEE MAGIC!", "Markdown", $privateKeyboard);
        }
    }
}

    // -------------------------------
    // 4. Handle Callback Queries
    // -------------------------------
    if (isset($update["callback_query"])) {
        $callback      = $update["callback_query"];
        $callbackData  = $callback["data"];
        $callback_id   = $callback["id"];
        $chat_id       = $callback["message"]["chat"]["id"];
        $user_id       = $callback["from"]["id"];

        // Store user data for callback query interactions
        if (isset($callback["from"])) {
            storeUserData($callback["from"]);
        }

        // Helper to build the dual‑channel join keyboard
        $joinKeyboard = buildInlineKeyboard([
            [
                ["text" => "🚀 Join Update Channel", "url" => $updateLink],
                ["text" => "🚀 Join Forced Channel", "url" => $forceSubLink],
            ],
            [
                ["text" => "🔄 Check Joined", "callback_data" => "check_join"]
            ]
        ]);

        // When user presses "Check Joined"
        if ($callbackData === "check_join") {
            $joinedPrimary = isUserJoined($user_id, $updateChannel);
            $joinedForced  = isUserJoined($user_id, $forceSubChannel);

            if ($joinedPrimary && $joinedForced) {
                answerCallbackQuery($callback_id, "✅ You’ve joined both channels!");
                // Show main menu
                $mainKeyboard = buildInlineKeyboard([
                    [
                        ["text" => "➕ Add Your Channel", "url" => "https://t.me/{$botUsername}?startchannel=&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info"]
                    ],
                    [
                        ["text" => "➕ Add Your Group", "url" => "https://t.me/{$botUsername}?startgroup&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info"]
                    ],
                    [
                        ["text" => "💡 More Cool Bots", "url" => $updateLink]
                    ],
                    [
                        ["text" => "100+ Multi Reactions😍🔥💖", "url" => "https://t.me/titanium_bots_channel/60"]
                    ]
                ]);
                $msg = "🎉 *Thank you for joining both channels!* \n\n" .
                       "Now you can use Auto Reaction Bot! \n\n" .
                       "Choose an option below:";
                sendMessage($chat_id, $msg, "Markdown", $mainKeyboard, "image.jpg");
            } else {
                answerCallbackQuery($callback_id, "❌ Not yet joined both.");
                sendMessage($chat_id,
                    "❌ You must join both channels to proceed. Please use the buttons below.",
                    "Markdown",
                    $joinKeyboard,
                    "image.jpg"
                );
            }
        }

        // When user presses "Use The Bot" in private auto-reaction handler
        if ($callbackData === "use_bot") {
            $joinedPrimary = isUserJoined($user_id, $updateChannel);
            $joinedForced  = isUserJoined($user_id, $forceSubChannel);

            if (!($joinedPrimary && $joinedForced)) {
                answerCallbackQuery($callback_id, "❌ You must join both first.");
                sendMessage($chat_id,
                    "🤖 *Welcome to Free AUTO REACTION Bot!* \n\n" .
                    "✨ To get started, please join both of our channels below:\n\n" .
                    "1️⃣ Update Channel\n2️⃣ Forced Subscription Channel\n\n" .
                    "Once you’ve joined, click _Check Joined_.",
                    "Markdown",
                    $joinKeyboard,
                    "image.jpg"
                );
            } else {
                answerCallbackQuery($callback_id, "✅ Started!");
                // Show main menu
                $mainKeyboard = buildInlineKeyboard([
                    [
                        ["text" => "➕ Add Your Channel", "url" => "https://t.me/{$botUsername}?startchannel=&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info"]
                    ],
                    [
                        ["text" => "➕ Add Your Group", "url" => "https://t.me/{$botUsername}?startgroup&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info"]
                    ],
                    [
                        ["text" => "💡 More Cool Bots", "url" => $updateLink]
                    ],
                    [
                        ["text" => "100+ Multi Reactions😍🔥💖", "url" => "https://t.me/titanium_bots_channel/60"]
                    ]
                ]);
                $msg = "🎉 *Thank you for joining both channels!* \n\n" .
                       "Now you can use Auto Reaction Bot! \n\n" .
                       "Choose an option below:";
                sendMessage($chat_id, $msg, "Markdown", $mainKeyboard, "image.jpg");
            }
        }
    }

    // End of PHP script
    ?>
